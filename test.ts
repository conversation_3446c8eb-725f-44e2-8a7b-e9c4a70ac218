/**
 * 优化版冒泡排序算法
 * @param arr 需要排序的数字数组
 * @returns 排序后的数组
 */
function bubbleSortOptimized(arr: number[]): number[] {
    // 获取数组长度
    const len = arr.length;

    // 外层循环：控制排序轮数
    for (let i = 0; i < len - 1; i++) {
        // 标记当前轮次是否发生交换
        let swapped = false;

        // 内层循环：比较相邻元素
        // 每轮排序后最后 i 个元素已经就位，所以可以减少比较次数
        for (let j = 0; j < len - 1 - i; j++) {
            // 如果前一个元素大于后一个元素，则交换它们的位置
            if (arr[j] > arr[j + 1]) {
                // 使用解构赋值进行交换
                [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
                swapped = true;
            }
        }

        // 如果本轮没有发生交换，说明数组已经有序，可以提前退出
        if (!swapped) break;
    }

    return arr;
}

